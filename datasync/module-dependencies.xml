<?xml version="1.0" encoding="UTF-8"?>
<!--
  Module Dependencies for DataSync module
  Ensures compatibility with Niagara 4.11+
-->
<module-dependencies>
  <!-- Core Niagara dependencies with minimum version constraints -->
  <depend>
    <name>baja</name>
    <vendor>Tridium</vendor>
    <min-version>4.11.0</min-version>
  </depend>
  
  <depend>
    <name>bajaui</name>
    <vendor>Tridium</vendor>
    <min-version>4.11.0</min-version>
  </depend>
  
  <depend>
    <name>workbench</name>
    <vendor>Tridium</vendor>
    <min-version>4.11.0</min-version>
  </depend>
  
  <depend>
    <name>gx</name>
    <vendor>Tridium</vendor>
    <min-version>4.11.0</min-version>
  </depend>
  
  <!-- Optional BACnet and Control dependencies -->
  <depend>
    <name>bacnet</name>
    <vendor>Tridium</vendor>
    <min-version>4.11.0</min-version>
    <optional>true</optional>
  </depend>
  
  <depend>
    <name>control</name>
    <vendor>Tridium</vendor>
    <min-version>4.11.0</min-version>
    <optional>true</optional>
  </depend>
</module-dependencies>