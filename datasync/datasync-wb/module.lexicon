#
# Lexicon for the datasync module.
#

# Tool registration
DataSyncTool.displayName = DataSync Tool
DataSyncTool.description = DataSync tool for Niagara Workbench
DataSyncTool.icon = module://icons/x16/tool.png

# View registration
DataSyncManagerView.displayName = DataSync Manager
DataSyncManagerView.description = Main view for managing DataSync connection profiles

# Table registration
DataSyncTable.displayName = DataSync Table
DataSyncTable.description = Table displaying DataSync connection profiles

# Connection Profile Components
ConnectionProfile.displayName = Connection Profile
ConnectionProfile.description = DataSync connection profile configuration
ConnectionProfileFolder.displayName = Connection Profile Folder
ConnectionProfileFolder.description = Folder for organizing connection profiles
ConnectionProfileContainer.displayName = Connection Profiles
ConnectionProfileContainer.description = Container for all connection profiles

# Connection Profile Manager
ConnectionProfileManager.displayName = Connection Profile Manager
ConnectionProfileManager.description = Manager for DataSync connection profiles with folder support

