/*
 * Copyright (c) 2024 MEA. All Rights Reserved.
 */

package com.mea.datasync.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BComponent;
import javax.baja.sys.BStation;
import javax.baja.sys.BString;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.model.BConnectionProfileContainer;
import com.mea.datasync.model.BConnectionProfileFolder;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

/**
 * BConnectionProfileManagerTest provides TestNG-based tests to verify
 * that our connection profile components work correctly.
 */
@NiagaraType
@Test(groups = { "datasync" })
public class BConnectionProfileManagerTest extends BTestNg {

//region /*+ ------------ <PERSON>EGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.test.BConnectionProfileManagerTest(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConnectionProfileManagerTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Test Setup and Teardown
////////////////////////////////////////////////////////////////

  @BeforeClass(alwaysRun = true)
  public void setupBeforeClass() throws Exception {
    // Initialize test station
    handler = createTestStation();
    handler.startStation();
    station = handler.getStation();

    root = new BComponent();
    station.add("root", root);
  }

  @AfterClass(alwaysRun = true)
  public void teardownAfterClass() throws Exception {
    // Cleanup
    handler.releaseStation();
  }

////////////////////////////////////////////////////////////////
// Test Methods
////////////////////////////////////////////////////////////////

  /**
   * Test creating and configuring connection profile components.
   */
  @Test(enabled = true)
  public void testConnectionProfileComponents() {
    // Clear any existing components
    root.removeAll();

    // Test 1: Create a connection profile container
    BConnectionProfileContainer container = new BConnectionProfileContainer();
    root.add("container", container);
    Assert.assertNotNull(container);
    Assert.assertEquals(container.getType().getTypeName(), "ConnectionProfileContainer");

    // Test 2: Create a connection profile folder
    BConnectionProfileFolder folder = new BConnectionProfileFolder();
    container.add("testFolder", folder);
    Assert.assertNotNull(folder);
    Assert.assertEquals(folder.getType().getTypeName(), "ConnectionProfileFolder");

    // Test 3: Create a connection profile
    BConnectionProfile profile = new BConnectionProfile();
    profile.setSourceType(BString.make("Excel"));
    profile.setSourcePath(BString.make("C:\\Test\\data.xlsx"));
    profile.setTargetHost(BString.make("localhost"));
    profile.setStatusString(BConnectionProfile.STATUS_NEVER_SYNCED);
    profile.setComponentsCreatedInt(0);

    folder.add("testProfile", profile);
    Assert.assertNotNull(profile);
    Assert.assertEquals(profile.getSourceType().toString(), "Excel");
    Assert.assertEquals(profile.getSourcePath().toString(), "C:\\Test\\data.xlsx");
    Assert.assertEquals(profile.getTargetHost().toString(), "localhost");
    Assert.assertEquals(profile.getStatusString(), BConnectionProfile.STATUS_NEVER_SYNCED);
    Assert.assertEquals(profile.getComponentsCreatedInt(), 0);
  }

  @Test(enabled = true)
  public void testProfileStatusUpdates() {
    // Clear any existing components
    root.removeAll();

    // Create test profile
    BConnectionProfile profile = new BConnectionProfile();
    root.add("testProfile", profile);

    // Test status updates
    profile.setStatusString(BConnectionProfile.STATUS_IN_PROGRESS);
    Assert.assertEquals(profile.getStatusString(), BConnectionProfile.STATUS_IN_PROGRESS);

    profile.setStatusString(BConnectionProfile.STATUS_SUCCESS);
    profile.setLastSync(BAbsTime.now());
    profile.setComponentsCreatedInt(25);

    Assert.assertEquals(profile.getStatusString(), BConnectionProfile.STATUS_SUCCESS);
    Assert.assertNotNull(profile.getLastSync());
    Assert.assertEquals(profile.getComponentsCreatedInt(), 25);

    // Test error status
    profile.setStatusString(BConnectionProfile.STATUS_ERROR);
    profile.setLastError(BString.make("Test error message"));

    Assert.assertEquals(profile.getStatusString(), BConnectionProfile.STATUS_ERROR);
    Assert.assertEquals(profile.getLastError().toString(), "Test error message");
  }

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

  private BStation station;
  private BComponent root;
  private TestStationHandler handler;
  }
}
