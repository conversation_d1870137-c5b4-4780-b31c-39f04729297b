<!-- Module Include File -->
<!-- Types -->
<types>
  <!-- com.mea.datasync.ui -->
  <!--com.mea.datasync.ui-->
  <type class="com.mea.datasync.ui.BDataSyncTool" name="DataSyncTool"/>
  <type class="com.mea.datasync.ui.BDataSyncManagerView" name="DataSyncManagerView"/>
  <type class="com.mea.datasync.ui.BDataSyncTable" name="DataSyncTable"/>
  <!-- DataSync Views - Register as agents on the tool type -->
  <type class="com.mea.datasync.ui.BDataSyncProfileView" name="DataSyncProfileView">
    <agent>
      <on type="datasync:DataSyncTool"/>
    </agent>
  </type>

  <!-- DataSync Model Components -->
  <type class="com.mea.datasync.model.BConnectionProfile" name="ConnectionProfile"/>
</types>