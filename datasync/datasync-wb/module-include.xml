<!-- Module Include File -->
<types>
  <!-- Tool Type -->
  <!--com.mea.datasync.ui-->
  <type class="com.mea.datasync.ui.BDataSyncTool" name="DataSyncTool"/>
  <!-- View Types - Register as agents on the tool type -->
  <type class="com.mea.datasync.ui.BDataSyncSimpleView" name="DataSyncSimpleView">
    <agent>
      <on type="datasync:DataSyncTool"/>
    </agent>
  </type>
  <type class="com.mea.datasync.ui.BDataSyncTableView" name="DataSyncTableView">
    <agent>
      <on type="datasync:DataSyncTool"/>
    </agent>
  </type>
  <type class="com.mea.datasync.ui.BDataSyncManagerView" name="DataSyncManagerView">
    <agent>
      <on type="datasync:DataSyncTool"/>
    </agent>
  </type>
  <type class="com.mea.datasync.ui.BDataSyncTable" name="DataSyncTable"/>
  <type class="com.mea.datasync.ui.BDataSyncTestView" name="DataSyncTestView">
    <agent>
      <on type="datasync:DataSyncTool"/>
    </agent>
  </type>

  <!-- Connection Profile Components -->
  <type class="com.mea.datasync.model.BConnectionProfile" name="ConnectionProfile"/>
  <type class="com.mea.datasync.model.BConnectionProfileFolder" name="ConnectionProfileFolder"/>
  <type class="com.mea.datasync.model.BConnectionProfileContainer" name="ConnectionProfileContainer"/>

  <!-- Connection Profile Manager -->
  <type class="com.mea.datasync.ui.BConnectionProfileManager" name="ConnectionProfileManager">
    <agent>
      <on type="datasync:DataSyncTool"/>
    </agent>
  </type>
</types>
<!-- Tool Registration for Tools Menu -->
<tools>
  <tool name="DataSync Manager" type="datasync:DataSyncTool"/>
</tools>