package com.mea.datasync.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Unit tests for the ConnectionProfile model class.
 */
public class ConnectionProfileTest {

    @Test
    public void testProfileCreation() {
        // 1. Setup - Define test data
        String name = "Test Profile 1";
        String sourceType = "Excel";
        String sourcePath = "C:/data/source.xlsx";
        String sheetName = "Sheet1";
        String targetHost = "localhost";
        String targetUsername = "admin";
        String targetPath = "station:|slot:/Drivers/Data";

        // 2. Action - Create the object
        ConnectionProfile profile = new ConnectionProfile(
            name, sourceType, sourcePath, sheetName, 
            targetHost, targetUsername, targetPath
        );

        // 3. Assertion - Verify the initial state
        assertNotNull("The created profile should not be null.", profile);
        assertEquals("Name should match the one provided in constructor.", name, profile.getName());
        assertEquals("Source Type should match.", sourceType, profile.getSourceType());
        assertEquals("Source Path should match.", sourcePath, profile.getSourcePath());
        assertEquals("Sheet Name should match.", sheetName, profile.getSheetName());
        assertEquals("Target Host should match.", targetHost, profile.getTargetHost());
        assertEquals("Target Username should match.", targetUsername, profile.getTargetUsername());
        assertEquals("Target Path should match.", targetPath, profile.getTargetPath());
        
        // Verify default values
        assertEquals("Initial status should be NEVER_SYNCED.", ConnectionProfile.SyncStatus.NEVER_SYNCED, profile.getStatus());
        assertEquals("Initial components created count should be 0.", 0, profile.getComponentsCreated());
        assertNull("Last sync time should be null initially.", profile.getLastSync());
        assertNull("Last error should be null initially.", profile.getLastError());
    }
    
    @Test
    public void testSettersAndGetters() {
        // 1. Setup - Create a basic profile
        ConnectionProfile profile = new ConnectionProfile("Initial Name", "", "", "", "", "", "");

        // 2. Action & 3. Assertion - Test each setter and getter pair
        String newName = "Updated Profile Name";
        profile.setName(newName);
        assertEquals("getName should return the updated name.", newName, profile.getName());
        
        profile.setStatus(ConnectionProfile.SyncStatus.SUCCESS);
        assertEquals("getStatus should return the new status.", ConnectionProfile.SyncStatus.SUCCESS, profile.getStatus());
        
        int components = 120;
        profile.setComponentsCreated(components);
        assertEquals("getComponentsCreated should return the updated count.", components, profile.getComponentsCreated());
        
        String error = "Failed to connect.";
        profile.setLastError(error);
        assertEquals("getLastError should return the new error message.", error, profile.getLastError());
    }
}
