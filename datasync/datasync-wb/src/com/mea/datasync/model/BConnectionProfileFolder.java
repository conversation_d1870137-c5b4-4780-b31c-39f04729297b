// In: com.mea.datasync.model
package com.mea.datasync.model;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BFolder;

/**
 * BConnectionProfileFolder is a container for organizing connection profiles
 * in a hierarchical structure. It extends BFolder to provide standard
 * folder functionality within the Niagara framework.
 */
@NiagaraType
public class BConnectionProfileFolder extends BFolder {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.model.BConnectionProfileFolder(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConnectionProfileFolder.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Default constructor.
   */
  public BConnectionProfileFolder() {
  }

////////////////////////////////////////////////////////////////
// BFolder Overrides
////////////////////////////////////////////////////////////////

  // Note: toString() is final in BObject, so we cannot override it
  // The display name will be handled by the Niagara framework
}
