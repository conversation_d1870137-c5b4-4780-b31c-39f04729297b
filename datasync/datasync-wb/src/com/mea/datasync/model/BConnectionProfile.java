// In: com.mea.datasync.model
package com.mea.datasync.model;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;

/**
 * BConnectionProfile represents a saved configuration for connecting to
 * an external data source (Excel file) and target Niagara station.
 * This is now a proper BComponent that can be managed by the Niagara framework.
 */
@NiagaraType
public class BConnectionProfile extends BComponent {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.model.BConnectionProfile(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConnectionProfile.class);

  //endregion Type

  //region Slots

  @NiagaraProperty(
    name = "sourceType",
    type = "baja:String",
    defaultValue = "BString.make(\"Excel\")"
  )
  public static final Property sourceType = newProperty(0, BString.make("Excel"), null);

  @NiagaraProperty(
    name = "sourcePath",
    type = "baja:String",
    defaultValue = "BString.DEFAULT"
  )
  public static final Property sourcePath = newProperty(0, BString.DEFAULT, null);

  @NiagaraProperty(
    name = "sheetName",
    type = "baja:String",
    defaultValue = "BString.DEFAULT"
  )
  public static final Property sheetName = newProperty(0, BString.DEFAULT, null);

  @NiagaraProperty(
    name = "targetHost",
    type = "baja:String",
    defaultValue = "BString.DEFAULT"
  )
  public static final Property targetHost = newProperty(0, BString.DEFAULT, null);

  @NiagaraProperty(
    name = "targetUsername",
    type = "baja:String",
    defaultValue = "BString.DEFAULT"
  )
  public static final Property targetUsername = newProperty(0, BString.DEFAULT, null);

  @NiagaraProperty(
    name = "targetPath",
    type = "baja:String",
    defaultValue = "BString.DEFAULT"
  )
  public static final Property targetPath = newProperty(0, BString.DEFAULT, null);

  @NiagaraProperty(
    name = "lastSync",
    type = "baja:AbsTime",
    defaultValue = "BAbsTime.NULL"
  )
  public static final Property lastSync = newProperty(0, BAbsTime.NULL, null);

  @NiagaraProperty(
    name = "status",
    type = "baja:String",
    defaultValue = "BString.make(\"Never Synced\")"
  )
  public static final Property status = newProperty(0, BString.make("Never Synced"), null);

  @NiagaraProperty(
    name = "componentsCreated",
    type = "baja:Int",
    defaultValue = "BInteger.make(0)"
  )
  public static final Property componentsCreated = newProperty(0, BInteger.make(0), null);

  @NiagaraProperty(
    name = "lastError",
    type = "baja:String",
    defaultValue = "BString.DEFAULT"
  )
  public static final Property lastError = newProperty(0, BString.DEFAULT, null);

  //endregion Slots

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
    
////////////////////////////////////////////////////////////////
// Property Access
////////////////////////////////////////////////////////////////

  public BString getSourceType() { return (BString)get(sourceType); }
  public void setSourceType(BString value) { set(sourceType, value, null); }

  public BString getSourcePath() { return (BString)get(sourcePath); }
  public void setSourcePath(BString value) { set(sourcePath, value, null); }

  public BString getSheetName() { return (BString)get(sheetName); }
  public void setSheetName(BString value) { set(sheetName, value, null); }

  public BString getTargetHost() { return (BString)get(targetHost); }
  public void setTargetHost(BString value) { set(targetHost, value, null); }

  public BString getTargetUsername() { return (BString)get(targetUsername); }
  public void setTargetUsername(BString value) { set(targetUsername, value, null); }

  public BString getTargetPath() { return (BString)get(targetPath); }
  public void setTargetPath(BString value) { set(targetPath, value, null); }

  public BAbsTime getLastSync() { return (BAbsTime)get(lastSync); }
  public void setLastSync(BAbsTime value) { set(lastSync, value, null); }

  public BString getStatus() { return (BString)get(status); }
  public void setStatus(BString value) { set(status, value, null); }

  public BInteger getComponentsCreated() { return (BInteger)get(componentsCreated); }
  public void setComponentsCreated(BInteger value) { set(componentsCreated, value, null); }

  public BString getLastError() { return (BString)get(lastError); }
  public void setLastError(BString value) { set(lastError, value, null); }
////////////////////////////////////////////////////////////////
// Convenience Methods
////////////////////////////////////////////////////////////////

  /**
   * Set status using string value for convenience
   */
  public void setStatusString(String statusStr) {
    setStatus(BString.make(statusStr));
  }

  /**
   * Get status as string for convenience
   */
  public String getStatusString() {
    return getStatus().toString();
  }

  /**
   * Set components created using int value for convenience
   */
  public void setComponentsCreatedInt(int count) {
    setComponentsCreated(BInteger.make(count));
  }

  /**
   * Get components created as int for convenience
   */
  public int getComponentsCreatedInt() {
    return getComponentsCreated().getInt();
  }

////////////////////////////////////////////////////////////////
// Constants for Status Values
////////////////////////////////////////////////////////////////

  public static final String STATUS_NEVER_SYNCED = "Never Synced";
  public static final String STATUS_SUCCESS = "Success";
  public static final String STATUS_ERROR = "Error";
  public static final String STATUS_IN_PROGRESS = "In Progress";
}
