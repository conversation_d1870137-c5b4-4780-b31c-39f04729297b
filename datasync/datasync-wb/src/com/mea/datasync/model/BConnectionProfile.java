// In: com.mea.datasync.model
package com.mea.datasync.model;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;

/**
 * BConnectionProfile represents a saved configuration for connecting to
 * an external data source (Excel file) and target Niagara station.
 */
@NiagaraType
public class BConnectionProfile extends BComponent {

//region /*+ ------------ <PERSON><PERSON>IN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.model.BConnectionProfile(2979906276)1.0$ @*/
/* Generated by Slot-o-Mat<PERSON> (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConnectionProfile.class);

  //endregion Type

  //region Slots

  public static final Property sourceType = newProperty(0, BString.make("Excel"), null);
  public static final Property sourcePath = newProperty(0, BString.DEFAULT, null);
  public static final Property targetHost = newProperty(0, BString.DEFAULT, null);
  public static final Property status = newProperty(0, BString.make("Never Synced"), null);

  //endregion Slots

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Property Access
////////////////////////////////////////////////////////////////

  public BString getSourceType() { return (BString)get(sourceType); }
  public void setSourceType(BString value) { set(sourceType, value, null); }

  public BString getSourcePath() { return (BString)get(sourcePath); }
  public void setSourcePath(BString value) { set(sourcePath, value, null); }

  public BString getTargetHost() { return (BString)get(targetHost); }
  public void setTargetHost(BString value) { set(targetHost, value, null); }

  public BString getStatus() { return (BString)get(status); }
  public void setStatus(BString value) { set(status, value, null); }

////////////////////////////////////////////////////////////////
// Convenience Methods
////////////////////////////////////////////////////////////////

  public void setStatusString(String statusStr) {
    setStatus(BString.make(statusStr));
  }

  public String getStatusString() {
    return getStatus().toString();
  }

////////////////////////////////////////////////////////////////
// Constants
////////////////////////////////////////////////////////////////

  public static final String STATUS_NEVER_SYNCED = "Never Synced";
  public static final String STATUS_SUCCESS = "Success";
  public static final String STATUS_ERROR = "Error";
  public static final String STATUS_IN_PROGRESS = "In Progress";
}
