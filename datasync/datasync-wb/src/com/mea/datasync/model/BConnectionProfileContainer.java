// In: com.mea.datasync.model
package com.mea.datasync.model;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BFolder;

/**
 * BConnectionProfileContainer is the root container for all connection profiles
 * and folders in the DataSync tool. It serves as the top-level container
 * similar to how BDriverContainer works for driver networks.
 */
@NiagaraType
public class BConnectionProfileContainer extends BFolder {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.model.BConnectionProfileContainer(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConnectionProfileContainer.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Default constructor.
   */
  public BConnectionProfileContainer() {
    // Note: setName() is not available in BFolder
    // The name will be set by the framework or parent
  }

////////////////////////////////////////////////////////////////
// BFolder Overrides
////////////////////////////////////////////////////////////////

  // Note: toString() is final in BObject, so we cannot override it
  // The display name will be handled by the Niagara framework

////////////////////////////////////////////////////////////////
// Container Management
////////////////////////////////////////////////////////////////

  /**
   * Get the default folder type for organizing profiles.
   */
  public Type getProfileFolderType() {
    return BConnectionProfileFolder.TYPE;
  }

  /**
   * Get the profile type that this container manages.
   */
  public Type getProfileType() {
    return BConnectionProfile.TYPE;
  }
}
