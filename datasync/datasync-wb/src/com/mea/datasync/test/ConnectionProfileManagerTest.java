// In: com.mea.datasync.test
package com.mea.datasync.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BString;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.workbench.view.BWbView;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.model.BConnectionProfileContainer;
import com.mea.datasync.model.BConnectionProfileFolder;

/**
 * ConnectionProfileManagerTest provides a simple test view to verify
 * that our connection profile components work correctly.
 */
@NiagaraType
public class ConnectionProfileManagerTest extends BWbView {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.test.ConnectionProfileManagerTest(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(ConnectionProfileManagerTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Default constructor.
   */
  public ConnectionProfileManagerTest() {
    testConnectionProfileComponents();
  }

////////////////////////////////////////////////////////////////
// Test Methods
////////////////////////////////////////////////////////////////

  /**
   * Test creating and configuring connection profile components.
   */
  private void testConnectionProfileComponents() {
    try {
      // Test 1: Create a connection profile container
      BConnectionProfileContainer container = new BConnectionProfileContainer();
      System.out.println("✓ Created BConnectionProfileContainer: " + container.getType().getTypeName());

      // Test 2: Create a connection profile folder
      BConnectionProfileFolder folder = new BConnectionProfileFolder();
      folder.setName("Test Folder");
      System.out.println("✓ Created BConnectionProfileFolder: " + folder.getName());

      // Test 3: Create a connection profile
      BConnectionProfile profile = new BConnectionProfile();
      profile.setName("Test Profile");
      profile.setSourceType(BString.make("Excel"));
      profile.setSourcePath(BString.make("C:\\Test\\data.xlsx"));
      profile.setTargetHost(BString.make("localhost"));
      profile.setStatusString(BConnectionProfile.STATUS_NEVER_SYNCED);
      profile.setComponentsCreatedInt(0);
      
      System.out.println("✓ Created BConnectionProfile: " + profile.getName());
      System.out.println("  - Source Type: " + profile.getSourceType());
      System.out.println("  - Source Path: " + profile.getSourcePath());
      System.out.println("  - Target Host: " + profile.getTargetHost());
      System.out.println("  - Status: " + profile.getStatusString());
      System.out.println("  - Components Created: " + profile.getComponentsCreatedInt());

      // Test 4: Update profile status
      profile.setStatusString(BConnectionProfile.STATUS_SUCCESS);
      profile.setLastSync(BAbsTime.now());
      profile.setComponentsCreatedInt(25);
      
      System.out.println("✓ Updated profile status:");
      System.out.println("  - Status: " + profile.getStatusString());
      System.out.println("  - Last Sync: " + profile.getLastSync());
      System.out.println("  - Components Created: " + profile.getComponentsCreatedInt());

      // Test 5: Add profile to folder (simulate hierarchy)
      System.out.println("✓ Connection Profile components are working correctly!");
      
    } catch (Exception e) {
      System.err.println("✗ Error testing connection profile components: " + e.getMessage());
      e.printStackTrace();
    }
  }
}
