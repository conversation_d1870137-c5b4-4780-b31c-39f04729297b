// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.sys.Type;
import javax.baja.workbench.mgr.MgrColumn;
import javax.baja.workbench.mgr.MgrTypeInfo;
import javax.baja.workbench.mgr.folder.BFolderManager;
import javax.baja.workbench.mgr.folder.FolderModel;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.model.BConnectionProfileFolder;

/**
 * ConnectionProfileModel manages the logical model of connection profiles
 * and folders in the DataSync tool. It extends FolderModel to provide
 * folder support similar to DeviceModel and PointModel.
 */
public class ConnectionProfileModel extends FolderModel {

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Constructor.
   */
  public ConnectionProfileModel(BFolderManager manager) {
    super(manager);
  }

////////////////////////////////////////////////////////////////
// FolderModel Implementation
////////////////////////////////////////////////////////////////

  /**
   * Get the folder type for organizing connection profiles.
   */
  @Override
  public Type getFolderType() {
    return BConnectionProfileFolder.TYPE;
  }

////////////////////////////////////////////////////////////////
// MgrModel Implementation
////////////////////////////////////////////////////////////////

  /**
   * Get the title to display for the table.
   */
  @Override
  protected String makeTableTitle() {
    return "Connection Profiles";
  }

  /**
   * Define the columns to display in the table.
   */
  @Override
  protected MgrColumn[] makeColumns() {
    return new MgrColumn[] {
      new MgrColumn.Path(MgrColumn.UNSEEN),
      new MgrColumn.Name(),
      new MgrColumn.Prop(BConnectionProfile.sourceType),
      new MgrColumn.Prop(BConnectionProfile.sourcePath),
      new MgrColumn.Prop(BConnectionProfile.targetHost),
      new MgrColumn.Prop(BConnectionProfile.status),
      new MgrColumn.Prop(BConnectionProfile.lastSync),
      new MgrColumn.Prop(BConnectionProfile.componentsCreated),
      new MgrColumn.Prop(BConnectionProfile.lastError, MgrColumn.UNSEEN)
    };
  }

  /**
   * Get the types to include in this manager.
   */
  @Override
  public Type[] getIncludeTypes() {
    return new Type[] { 
      BConnectionProfile.TYPE, 
      BConnectionProfileFolder.TYPE 
    };
  }

  /**
   * Get the types that can be created via the "New" operation.
   */
  @Override
  public MgrTypeInfo[] getNewTypes() {
    return MgrTypeInfo.makeArray(BConnectionProfile.TYPE);
  }

  /**
   * Get the base type supported by the new operation.
   */
  @Override
  public Type getBaseNewType() {
    return BConnectionProfile.TYPE;
  }
}
