// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.table.BTable;
import javax.baja.ui.table.DynamicTableModel;
import javax.baja.ui.table.TableModel;
import javax.baja.ui.table.TableSelection;

import com.mea.datasync.model.BConnectionProfile;

/**
 * BDataSyncTable displays connection profiles and their sync status
 * in a table format following Niagara UI patterns.
 */
@NiagaraType
public class BDataSyncTable extends BTable {

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

    public static final Type TYPE = Sys.loadType(BDataSyncTable.class);
    
    @Override
    public Type getType() { 
        return TYPE; 
    }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

    public BDataSyncTable() {
        // Initialize with dummy data
        this.profiles = createDummyData();
        
        // Set up table components following Niagara pattern
        setModel(new DynamicTableModel(new Model()));
        setSelection(new Selection());
    }

////////////////////////////////////////////////////////////////
// Data Management
////////////////////////////////////////////////////////////////

    /**
     * Create dummy data for demonstration
     * TODO: Replace with actual BConnectionProfile components
     */
    private List<BConnectionProfile> createDummyData() {
        List<BConnectionProfile> data = new ArrayList<>();

        // For now, return empty list since we'll use the manager framework
        // The actual profiles will be managed by the BConnectionProfileManager

        return data;
    }

    /**
     * Get the selected connection profile or null if no selection.
     */
    public BConnectionProfile getSelectedProfile() {
        int sel = getSelection().getRow();
        if (sel < 0 || sel >= profiles.size()) return null;
        return profiles.get(sel);
    }

////////////////////////////////////////////////////////////////
// Table Model
////////////////////////////////////////////////////////////////

    class Model extends TableModel {
        
        @Override
        public int getRowCount() {
            return profiles.size();
        }
        
        @Override
        public int getColumnCount() {
            return COLUMN_NAMES.length;
        }
        
        @Override
        public String getColumnName(int col) {
            return COLUMN_NAMES[col];
        }
        
        @Override
        public Object getValueAt(int row, int col) {
            if (row < 0 || row >= profiles.size()) return "";
            
            BConnectionProfile profile = profiles.get(row);
            switch (col) {
                case COL_NAME: return profile.getName();
                case COL_SOURCE: return profile.getSourcePath();
                case COL_TARGET: return profile.getTargetHost();
                case COL_STATUS: return profile.getStatus();
                case COL_LAST_SYNC: 
                    BAbsTime lastSync = profile.getLastSync();
                    if (lastSync == null) return "Never";
                    return DATE_FORMAT.format(new Date(lastSync.getMillis()));
                case COL_COMPONENTS: return Integer.toString(profile.getComponentsCreatedInt());
                default: return "";
            }
        }
        
        @Override
        public boolean isColumnSortable(int col) {
            return true;
        }
    }

////////////////////////////////////////////////////////////////
// Selection
////////////////////////////////////////////////////////////////

    class Selection extends TableSelection {
        @Override
        public void updateTable() {
            super.updateTable();
            // Fire selection changed event for view updates
            fireSelectionChanged();
        }
    }
    
    /**
     * Add method to refresh table data
     */
    public void refreshData() {
        // Clear current selection
        getSelection().deselectAll();
        
        // Notify the table that data has changed using Niagara's method
        DynamicTableModel dtm = (DynamicTableModel) getModel();
        dtm.updateTable(); // This will trigger relayout and fire tableModified event
    }
    
    /**
     * Add method to update profile data
     */
    public void setProfiles(List<BConnectionProfile> profiles) {
        this.profiles = profiles;
        refreshData();
    }
    
    /**
     * Fire selection changed event
     */
    private void fireSelectionChanged() {
        // Notify listeners that selection has changed
        // This can be used by the view to enable/disable buttons
    }

////////////////////////////////////////////////////////////////
// Constants
////////////////////////////////////////////////////////////////

    private static final int COL_NAME = 0;
    private static final int COL_SOURCE = 1;
    private static final int COL_TARGET = 2;
    private static final int COL_STATUS = 3;
    private static final int COL_LAST_SYNC = 4;
    private static final int COL_COMPONENTS = 5;
    
    private static final String[] COLUMN_NAMES = {
        "Profile Name",
        "Source File", 
        "Target Station",
        "Status",
        "Last Sync",
        "Components"
    };
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM/dd/yyyy HH:mm");

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

    private List<BConnectionProfile> profiles;
}
