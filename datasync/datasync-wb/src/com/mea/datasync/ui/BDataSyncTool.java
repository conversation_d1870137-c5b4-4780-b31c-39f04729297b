// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.workbench.tool.BWbNavNodeTool;

import com.mea.datasync.model.BConnectionProfile;

/**
 * BDataSyncTool serves as the entry point for the N4-DataSync module
 * within Niagara Workbench. It extends BWbNavNodeTool to appear in the
 * Tools menu and as a navigable node under the 'tool:' scheme.
 *
 * When selected from the Tools menu, it automatically opens the default
 * view associated with this tool, which will be the DataSync Manager view.
 */
@NiagaraType
public class BDataSyncTool extends BWbNavNodeTool {

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

    /**
     * Required Type field for all BObject subclasses.
     * This registers your class with the Niagara Type system.
     */
    public static final Type TYPE = Sys.loadType(BDataSyncTool.class);

    /**
     * Get the Type of this object.
     * @return the Type
     */
    public Type getType() {
        return TYPE;
    }

////////////////////////////////////////////////////////////////
// Properties
////////////////////////////////////////////////////////////////

    /**
     * Container for connection profiles
     */
    public static final Property profiles = newProperty(0, new BComponent(), null);

    /**
     * Get the profiles container
     */
    public BComponent getProfiles() {
        return (BComponent) get(profiles);
    }

    /**
     * Set the profiles container
     */
    public void setProfiles(BComponent value) {
        set(profiles, value, null);
    }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

    /**
     * Default constructor.
     * Tools are typically singletons, so no public constructor is needed for external instantiation.
     */
    public BDataSyncTool() {
        // Initialize with sample connection profiles for testing
        initializeSampleProfiles();
    }

////////////////////////////////////////////////////////////////
// Initialization
////////////////////////////////////////////////////////////////

    /**
     * Initialize with some sample connection profiles for testing
     */
    private void initializeSampleProfiles() {
        // Add connection profiles directly as children of this tool
        // The manager will find them automatically

        // Sample Profile 1
        BConnectionProfile profile1 = new BConnectionProfile();
        profile1.setSourceType(BString.make("Excel"));
        profile1.setSourcePath(BString.make("C:\\Data\\BuildingA_HVAC.xlsx"));
        profile1.setTargetHost(BString.make("*************"));
        profile1.setStatus(BString.make(BConnectionProfile.STATUS_SUCCESS));
        this.add("buildingA", profile1);

        // Sample Profile 2
        BConnectionProfile profile2 = new BConnectionProfile();
        profile2.setSourceType(BString.make("Excel"));
        profile2.setSourcePath(BString.make("C:\\Data\\BuildingB_Lighting.xlsx"));
        profile2.setTargetHost(BString.make("*************"));
        profile2.setStatus(BString.make(BConnectionProfile.STATUS_ERROR));
        this.add("buildingB", profile2);

        // Sample Profile 3
        BConnectionProfile profile3 = new BConnectionProfile();
        profile3.setSourceType(BString.make("Excel"));
        profile3.setSourcePath(BString.make("C:\\Data\\ChillerPlant.xlsx"));
        profile3.setTargetHost(BString.make("*************"));
        profile3.setStatus(BString.make(BConnectionProfile.STATUS_NEVER_SYNCED));
        this.add("chillerPlant", profile3);
    }

    // Note: BWbNavNodeTool automatically handles the invoke() method
    // by hyperlinking to the default view associated with this tool's ORD.
    // The tool's ORD will be "tool:com.mea.datasync.ui.BDataSyncTool|slot:/"
}
