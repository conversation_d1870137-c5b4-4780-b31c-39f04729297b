// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.workbench.tool.BWbNavNodeTool;

import com.mea.datasync.model.BConnectionProfileContainer;

/**
 * BDataSyncTool serves as the entry point for the N4-DataSync module
 * within Niagara Workbench. It extends BWbNavNodeTool to appear in the
 * Tools menu and as a navigable node under the 'tool:' scheme.
 *
 * When selected from the Tools menu, it automatically opens the default
 * view associated with this tool, which will be the DataSync Manager view.
 */
@NiagaraType
public class BDataSyncTool extends BWbNavNodeTool {

//region /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSyncTool(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSyncTool.class);

  //endregion Type

  //region Slots

  @NiagaraProperty(
    name = "profiles",
    type = "datasync:ConnectionProfileContainer",
    defaultValue = "new BConnectionProfileContainer()"
  )
  public static final Property profiles = newProperty(0, new BConnectionProfileContainer(), null);

  //endregion Slots

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Property Access
////////////////////////////////////////////////////////////////

  /**
   * Get the connection profiles container.
   */
  public BConnectionProfileContainer getProfiles() {
    return (BConnectionProfileContainer) get(profiles);
  }

  /**
   * Set the connection profiles container.
   */
  public void setProfiles(BConnectionProfileContainer value) {
    set(profiles, value, null);
  }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Default constructor.
   * Tools are typically singletons, so no public constructor is needed for external instantiation.
   */
  public BDataSyncTool() {
  }

////////////////////////////////////////////////////////////////
// BWbNavNodeTool Implementation
////////////////////////////////////////////////////////////////

  /**
   * Initialize the tool with default connection profile container.
   */
  public void started() throws Exception {
    super.started();

    // Ensure we have a profiles container
    if (getProfiles() == null) {
      setProfiles(new BConnectionProfileContainer());
    }
  }

  // Note: BWbNavNodeTool automatically handles the invoke() method
  // by hyperlinking to the default view associated with this tool's ORD.
  // The tool's ORD will be "tool:com.mea.datasync.ui.BDataSyncTool|slot:/"
}
