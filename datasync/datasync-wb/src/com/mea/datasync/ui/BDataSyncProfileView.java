// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BLabel;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BEdgePane;
import javax.baja.workbench.view.BWbView;

/**
 * BDataSyncProfileView provides a simple view for the DataSync tool
 * that displays basic information about connection profiles.
 */
@NiagaraType(
  agent = @AgentOn(
    types = { "datasync:DataSyncTool" }
  )
)
public class BDataSyncProfileView extends BWbView {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSyncProfileView(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSyncProfileView.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// BWbView Implementation
////////////////////////////////////////////////////////////////

  /**
   * Create the widget for this view.
   */
  protected BWidget makeWidget() {
    BEdgePane pane = new BEdgePane();

    // Add a simple label for now
    BLabel titleLabel = new BLabel("DataSync Connection Profiles");
    pane.setCenter(titleLabel);

    // Add some basic information
    BLabel infoLabel = new BLabel("This view will display and manage connection profiles for data synchronization.");
    pane.setBottom(infoLabel);

    return pane;
  }
}
