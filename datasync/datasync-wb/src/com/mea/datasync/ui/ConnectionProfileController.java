// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.workbench.mgr.folder.BFolderManager;
import javax.baja.workbench.mgr.folder.FolderController;

/**
 * ConnectionProfileController handles user interactions and commands
 * for the connection profile manager. It extends FolderController to
 * provide folder management capabilities.
 */
public class ConnectionProfileController extends FolderController {

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Constructor.
   */
  public ConnectionProfileController(BFolderManager manager) {
    super(manager);
  }
}
