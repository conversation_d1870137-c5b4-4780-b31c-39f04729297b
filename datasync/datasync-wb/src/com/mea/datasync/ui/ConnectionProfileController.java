// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.sys.BComponent;
import javax.baja.ui.HyperlinkInfo;
import javax.baja.ui.event.BMouseEvent;
import javax.baja.workbench.BWbShell;
import javax.baja.workbench.mgr.BMgrTable;
import javax.baja.workbench.mgr.MgrController.IMgrCommand;
import javax.baja.workbench.mgr.folder.BFolderManager;
import javax.baja.workbench.mgr.folder.FolderController;

import com.mea.datasync.model.BConnectionProfile;

/**
 * ConnectionProfileController handles user interactions and commands
 * for the connection profile manager. It extends FolderController to
 * provide folder management capabilities.
 */
public class ConnectionProfileController extends FolderController {

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  /**
   * Constructor.
   */
  public ConnectionProfileController(BFolderManager manager) {
    super(manager);
  }

////////////////////////////////////////////////////////////////
// FolderController Implementation
////////////////////////////////////////////////////////////////

  /**
   * Handle double-click on table cells.
   */
  @Override
  public void cellDoubleClicked(BMgrTable table, BMouseEvent event, int row, int col) {
    BComponent comp = table.getComponentAt(row);
    BWbShell shell = getManager().getWbShell();
    
    if (comp != null && shell != null) {
      if (comp instanceof BConnectionProfile) {
        // For connection profiles, we could open a detailed view or edit dialog
        // For now, just navigate to the component
        shell.hyperlink(new HyperlinkInfo(comp.getNavOrd(), event));
      } else {
        // For folders, use default navigation
        shell.hyperlink(new HyperlinkInfo(comp.getNavOrd(), event));
      }
    }
  }

////////////////////////////////////////////////////////////////
// Command Customization
////////////////////////////////////////////////////////////////

  /**
   * Customize the commands available for connection profiles.
   */
  @Override
  protected IMgrCommand[] makeCommands() {
    IMgrCommand[] baseCommands = super.makeCommands();
    
    // Add custom commands for sync operations
    IMgrCommand[] customCommands = new IMgrCommand[baseCommands.length + 1];
    System.arraycopy(baseCommands, 0, customCommands, 0, baseCommands.length);
    
    // Add sync command
    customCommands[baseCommands.length] = syncCommand;
    
    return customCommands;
  }

////////////////////////////////////////////////////////////////
// Custom Commands
////////////////////////////////////////////////////////////////

  /**
   * Command to sync a selected connection profile.
   */
  private final IMgrCommand syncCommand = new IMgrCommand() {
    public String getLabel() { return "Sync Now"; }

    public String getDescription() { return "Execute sync for selected connection profile"; }

    public int getFlags() { return ACTION_BAR | POPUP; }

    public void setFlags(int flags) { /* Not implemented */ }

    public boolean isEnabled() {
      BComponent[] selected = getManager().getModel().getTable().getSelectedComponents();
      return selected.length == 1 && selected[0] instanceof BConnectionProfile;
    }

    public void doInvoke() throws Exception {
      BComponent[] selected = getManager().getModel().getTable().getSelectedComponents();
      if (selected.length == 1 && selected[0] instanceof BConnectionProfile) {
        BConnectionProfile profile = (BConnectionProfile) selected[0];
        // TODO: Implement actual sync logic
        System.out.println("Syncing profile: " + profile.getName());

        // Update status to show sync in progress
        profile.setStatusString(BConnectionProfile.STATUS_IN_PROGRESS);

        // Here you would call your actual sync implementation
        // For now, just simulate success
        profile.setStatusString(BConnectionProfile.STATUS_SUCCESS);
        profile.setLastSync(javax.baja.sys.BAbsTime.now());
      }
    }
  };
}
