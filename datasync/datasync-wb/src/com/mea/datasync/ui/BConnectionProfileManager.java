// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.workbench.mgr.MgrController;
import javax.baja.workbench.mgr.MgrModel;
import javax.baja.workbench.mgr.MgrState;
import javax.baja.workbench.mgr.folder.BFolderManager;
import javax.baja.workbench.mgr.folder.FolderState;

/**
 * BConnectionProfileManager provides a table-based view for managing
 * connection profiles in the DataSync tool. It extends BFolderManager
 * to provide folder support similar to BDeviceManager and BPointManager.
 */
@NiagaraType
public class BConnectionProfileManager extends BFolderManager {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BConnectionProfileManager(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConnectionProfileManager.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// BFolderManager Implementation
////////////////////////////////////////////////////////////////

  /**
   * Create the model for managing connection profiles.
   */
  @Override
  protected MgrModel makeModel() {
    return new ConnectionProfileModel(this);
  }

  /**
   * Create the controller for handling user interactions.
   */
  @Override
  protected MgrController makeController() {
    return new ConnectionProfileController(this);
  }

  /**
   * Create the state manager for the view.
   */
  @Override
  protected MgrState makeState() {
    return new FolderState();
  }
}
