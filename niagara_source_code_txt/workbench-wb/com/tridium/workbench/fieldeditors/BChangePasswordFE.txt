/*
 * Copyright 2017 Tridium, Inc. All Rights Reserved.
 */
package com.tridium.workbench.fieldeditors;

import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.logging.Logger;

import javax.baja.gx.BImage;
import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.security.BPassword;
import javax.baja.sys.Action;
import javax.baja.sys.BIcon;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Localizable;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BLabel;
import javax.baja.ui.BNullWidget;
import javax.baja.ui.pane.BGridPane;
import javax.baja.user.BPasswordStrength;
import javax.baja.util.Lexicon;
import javax.baja.util.LexiconText;
import javax.baja.workbench.CannotSaveException;
import javax.baja.workbench.fieldeditor.BWbFieldEditor;

import com.tridium.ui.util.LabelUtil;

/**
 * This is a generic field editor for changing a password. It has 3 fields:
 * <ul>
 *   <li>Current password</li>
 *   <li>New password</li>
 *   <li>Confirm password</li>
 * </ul>
 *
 * <p>This field editor takes care of checking if the new/confirm passwords match,
 * and enforces password strength.</p>
 *
 * <AUTHOR> Coggan
 * @creation 11/10/2017
 * @since Niagara 4.5
 */
@NiagaraType
@NiagaraAction(
  name = "modified"
)
public class BChangePasswordFE
  extends BWbFieldEditor
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.tridium.workbench.fieldeditors.BChangePasswordFE(2154971492)1.0$ @*/
/* Generated Thu Jun 02 14:30:07 EDT 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Action "modified"

  /**
   * Slot for the {@code modified} action.
   * @see #modified()
   */
  public static final Action modified = newAction(0, null);

  /**
   * Invoke the {@code modified} action.
   * @see #modified
   */
  public void modified() { invoke(modified, null, null); }

  //endregion Action "modified"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BChangePasswordFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
////////////////////////////////////////////////////////////////
// Contructor
////////////////////////////////////////////////////////////////

  /**
   * Creates a BChangePasswordFE, with the following requirements:
   * <ul>
   *   <li>The password must meet the supplied {@link BPasswordStrength} requirements</li>
   *   <li>The new password must be entered twice.</li>
   *   <li>If a current passphrase is loaded (via loadValue()), that value must be entered correctly.</li>
   * </ul>
   * @param passwordStrength The strength requirements for the new password.
   */
  public BChangePasswordFE(BPasswordStrength passwordStrength)
  {
    this(passwordStrength, null);
  }

  /**
   * Creates a BChangePasswordFE, with the following requirements:
   * <ul>
   *   <li>The password must meet the supplied {@link BPasswordStrength} requirements</li>
   *   <li>The new password must be entered twice.</li>
   *   <li>If a current passphrase is loaded (via loadValue()), that value must be entered correctly.</li>
   * </ul>
   * @param passwordStrength The strength requirements for the new password.
   * @param passwordTerm An alternate term for "password" (e.g. "passphrase")
   */
  public BChangePasswordFE(BPasswordStrength passwordStrength, String passwordTerm)
  {
    this.passwordStrength = passwordStrength;

    passwordTermArgument = new Object[] { (passwordTerm != null) ? passwordTerm : lex.getText("changePassword.passwordTextValue.default")};

    // Build the warning indicators
    newPasswordIndicator = new BLabel(BImage.make(ICON_WARNING),
      lex.getText("changePasswordFE.new.requirementsNotMet", passwordTermArgument));
    confirmNewPasswordIndicator = new BLabel(BImage.make(ICON_WARNING),
      lex.getText("changePasswordFE.confirm.notConfirmed", passwordTermArgument));

    // Build the password field editors
    BGridPane passwordPane = new BGridPane(3);
    // -- Current password
    LabelUtil.addLabelWidgetPair(passwordPane, lex.getText("changePasswordFE.current.label", passwordTermArgument), currentPasswordFE);
    passwordPane.add(null, new BNullWidget());
    // -- New password
    LabelUtil.addLabelWidgetPair(passwordPane, lex.getText("changePasswordFE.new.label", passwordTermArgument), newPasswordFE);
    passwordPane.add(null, newPasswordIndicator);
    // -- Confirm new password
    LabelUtil.addLabelWidgetPair(passwordPane, lex.getText("changePasswordFE.confirm.label", passwordTermArgument), confirmNewPasswordFE);
    confirmNewPasswordIndicator.setVisible(false);
    passwordPane.add(null, confirmNewPasswordIndicator);

    // Link to the inner field editors
    linkTo(currentPasswordFE, BPasswordFE.modified, modified);
    linkTo(newPasswordFE, BPasswordFE.modified, modified);
    linkTo(confirmNewPasswordFE, BPasswordFE.modified, modified);

    setContent(passwordPane);
  }

////////////////////////////////////////////////////////////////
// Actions
////////////////////////////////////////////////////////////////
  public void doModified()
  {
    setModified();

    try
    {
      // Get the values from the field editors
      BPassword newPassword = (BPassword) newPasswordFE.saveValue();
      if (newPassword == null) newPassword = BLANK_PASSWORD;
      BPassword confirmNewPassword = (BPassword) confirmNewPasswordFE.saveValue();
      if (confirmNewPassword == null) confirmNewPassword = BLANK_PASSWORD;

      // Check if the new password is strong enough
      if (passwordStrength.isPasswordValid(AccessController.doPrivileged((PrivilegedAction<String>)newPassword::getValue).toCharArray(), null))
        newPasswordIndicator.setVisible(false);
      else
        newPasswordIndicator.setVisible(true);

      // Check if the passwords match
      if (AccessController.doPrivileged((PrivilegedAction<String>)newPassword::getValue).equals(AccessController.doPrivileged((PrivilegedAction<String>)confirmNewPassword::getValue)))
        confirmNewPasswordIndicator.setVisible(false);
      else
        confirmNewPasswordIndicator.setVisible(true);
    }
    catch(Exception ignore)
    {
      // Ignore this. We just won't update the icons.
    }
  }


////////////////////////////////////////////////////////////////
// Access
////////////////////////////////////////////////////////////////
  /**
   * Determines if the new password entered meets password strength requirements
   * and if the confirm password matches the new password.
   * @return True is the new password is valid, false otherwise.
   */
  public boolean isNewPasswordValid(Consumer<Localizable> messageConsumer)
  {
    boolean valid = true;
    List<Localizable> errors = new ArrayList<>();
    errors.add(LexiconText.toLocalizable("workbench", "changePasswordFE.invalid", passwordTermArgument));

    try
    {
      BPassword currentPassword = (BPassword)currentPasswordFE.saveValue();
      BPassword newPassword = (BPassword)newPasswordFE.saveValue();
      BPassword confirmNewPassword = (BPassword)confirmNewPasswordFE.saveValue();

      if (currentPassword == null) currentPassword = BLANK_PASSWORD;
      if (newPassword == null) newPassword = BLANK_PASSWORD;
      if (confirmNewPassword == null) confirmNewPassword = BLANK_PASSWORD;

      AtomicReference<Localizable> passwordStrengthError = new AtomicReference<>();
      if (!passwordStrength.isPasswordValid(AccessController.doPrivileged((PrivilegedAction<String>)newPassword::getValue).toCharArray(), passwordStrengthError::set))
      {
        valid = false;
        errors.add(passwordStrengthError.get());
      }

      if (!AccessController.doPrivileged((PrivilegedAction<String>)newPassword::getValue).equals(AccessController.doPrivileged((PrivilegedAction<String>)confirmNewPassword::getValue)))
      {
        valid = false;
        errors.add(LexiconText.toLocalizable("workbench", "changePasswordFE.invalid.match", passwordTermArgument));
      }

      if (actualCurrentPassword != null && !actualCurrentPassword.validate(currentPassword))
      {
        valid = false;
        errors.add(LexiconText.toLocalizable("workbench", "changePasswordFE.invalid.current", passwordTermArgument));
      }
    }
    catch(Exception e)
    {
      valid = false;
      LOG.warning("Could not verify that password meets requirements. Cause is: " + e.getMessage());
    }

    if (messageConsumer != null) messageConsumer.accept(Localizable.concatenate("\n", errors));
    return valid;
  }

  /**
   * Gets the value entered in the "Current Password" field.
   * @return A BPassword with the value entered in the "Current Password" field, or null if the password
   *         could not be retrieved.
   */
  public BPassword getCurrentPassword()
  {
    try
    {
      return (BPassword) currentPasswordFE.saveValue();
    }
    catch (Exception e)
    {
      LOG.severe("Could not get current password from field editor. Cause is: " + e.getMessage());
    }

    return null;
  }


////////////////////////////////////////////////////////////////
// BWbFieldEditor
////////////////////////////////////////////////////////////////
  /**
   * Saves the value entered in the "New Password" field.
   * @return A BPassword with the value entered in the "New Password" field
   * @throws CannotSaveException if the password is not valid (doesn't meet requirements, or is not confirmed),
   *                             or if the newPasswordFE could not be saved for some reason.
   */
  @Override
  protected BObject doSaveValue(BObject value, Context cx) throws CannotSaveException
  {
    // Check if the new password meets requirements
    AtomicReference<Localizable> errors = new AtomicReference<>();
    if (!isNewPasswordValid(errors::set))
    {
      throw new CannotSaveException(errors.get().toString(cx));
    }

    // If the password meets requirements, save it
    try
    {
      BPassword newPassword = (BPassword) newPasswordFE.saveValue(cx);
      if (newPassword == null) newPassword = BLANK_PASSWORD;
      return newPassword;
    }
    catch (Exception e)
    {
      throw new CannotSaveException(lex.getText("changePasswordFE.save.failed", new Object[] {passwordTermArgument[0], e.getMessage()}));
    }
  }

  @Override
  protected void doLoadValue(BObject value, Context context) throws Exception
  {
    if (value instanceof BPassword)
      actualCurrentPassword = (BPassword)value;
  }

////////////////////////////////////////////////////////////////
// Constants
////////////////////////////////////////////////////////////////
  private static BIcon ICON_WARNING = BIcon.std("warning.png");
  private static final BPassword BLANK_PASSWORD = BPassword.make("");
  private static final Logger LOG = Logger.getLogger("workbench");


////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////
  // Lexicon
  private Lexicon lex = Lexicon.make("workbench");

  // The loaded password, if any
  private BPassword actualCurrentPassword = null;

  // The password field editors
  private BPasswordFE currentPasswordFE = new BPasswordFE();
  private BPasswordFE newPasswordFE = new BPasswordFE();
  private BPasswordFE confirmNewPasswordFE = new BPasswordFE();

  // The indicators for whether a password field is valid
  private BLabel newPasswordIndicator;
  private BLabel confirmNewPasswordIndicator;

  private final Object[] passwordTermArgument;
  private final BPasswordStrength passwordStrength;
}
