/*
 * Copyright 2022 Tridium, Inc. All Rights Reserved.
 */
package com.tridium.workbench.fieldeditors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.security.BPassword;
import javax.baja.sys.BObject;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BToggleButton;
import javax.baja.ui.ToggleCommand;
import javax.baja.ui.pane.BGridPane;
import javax.baja.util.BFormat;
import javax.baja.util.Lexicon;

/**
 * BDefaultPasswordFE allows viewing and editing of a BPassword
 * with the ability to "reset" it to default.
 *
 * <AUTHOR>
 * @since Niagara 4.13
 */
@NiagaraType
public class BDefaultPasswordFE
  extends BPasswordFE
{
//region /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.tridium.workbench.fieldeditors.BDefaultPasswordFE(2979906276)1.0$ @*/
/* Generated Fri Sep 23 17:24:35 EDT 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDefaultPasswordFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
public BDefaultPasswordFE()
{
  this(2);
}

  @SuppressWarnings("deprecation")
  public BDefaultPasswordFE(int columns)
  {
    defaultCheckBox = new BCheckBox(new DefaultCommand());
    passwordRenderer = new javax.baja.ui.text.PlaceholderPasswordRenderer();
    passwordField.setRenderer(passwordRenderer);
    linkTo("linkD", defaultCheckBox, BToggleButton.actionPerformed, modified);
    linkTo("linkE", defaultCheckBox, BToggleButton.actionPerformed, setModified);
    BGridPane passwordPane = new BGridPane(columns);
    setContent(passwordPane);
    passwordPane.add("passwordField", passwordField);
    passwordPane.add("defaultCheckbox", defaultCheckBox);
  }

  @Override
  protected void doSetReadonly(boolean readonly)
  {
    super.doSetReadonly(readonly);
    defaultCheckBox.setEnabled(!readonly);
  }

  @Override
  public void resetFields()
  {
    super.resetFields();
    passwordField.setEditable(true);
    defaultCheckBox.setSelected(false);
  }

  @Override
  protected void doLoadValue(BObject value, Context cx)
  {
    if (firstLoad)
    {
      resetFields();

      if (cx != null)
      {
        BString m = (BString)cx.getFacet(BPassword.PLACEHOLDER_TEXT);
        if (m != null && !m.getString().isEmpty())
        {
          passwordRenderer.setBackgroundText(BFormat.format(m.toString(), null, cx));
        }
      }
    }

    changed = false;
    checkPasswordChangeAllowed();
    firstLoad = false;
  }

  @Override
  protected BObject doSaveValue(BObject value, Context cx)
  {
    if (changed)
    {
      if (defaultCheckBox.isVisible())
      {
        if (!defaultCheckBox.getSelected())
        {
          String str = passwordField.getText();
          return BPassword.make(str, cx);
        }
        else
        {
          return BPassword.DEFAULT;
        }
      }
      else
      {
        String str = passwordField.getText();
        return BPassword.make(str, cx);
      }
    }
    else
    {
      return getCurrentValue();
    }
  }

  @Override
  public void doModified()
  {
    if (defaultCheckBox.isVisible())
    {
      passwordField.setEnabled(!defaultCheckBox.getSelected());
    }
    passwordRenderer.setBackgroundText(null);
    changed = true;
  }

  private class DefaultCommand
    extends ToggleCommand
  {
    public DefaultCommand()
    {
      super(BDefaultPasswordFE.this, lex, "default.password.checkbox");
    }
  }

  public static final Lexicon lex = Lexicon.make("workbench");
  private final BCheckBox defaultCheckBox;

  @SuppressWarnings("deprecation")
  private final javax.baja.ui.text.PlaceholderPasswordRenderer passwordRenderer;
  private boolean firstLoad = true;
}
